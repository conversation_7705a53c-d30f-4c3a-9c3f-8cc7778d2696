package com.genco.admin.task.order;

import com.genco.common.constants.Constants;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.utils.RedisUtil;
import com.genco.service.model.tiktok.OrderPullTask;
import com.genco.service.service.OrderPullTaskService;
import com.genco.service.service.OrderTaskService;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.TiktokOrderSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * TikTok订单分页拉取定时任务
 */
@Component
@Configuration
@EnableScheduling
public class OrderTiktokPullTask {
    private static final Logger logger = LoggerFactory.getLogger(OrderTiktokPullTask.class);
    @Autowired
    private OrderPullTaskService orderPullTaskService;
    @Autowired
    private OrderTaskService orderTaskService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private TiktokOrderSyncService tiktokOrderSyncService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 每5分钟扫描任务表，抢占并处理待拉取任务
     */
    @Scheduled(fixedDelay = 300000)
    public void pullTiktokOrders() {
        logger.debug("TikTok订单拉取定时任务开始执行");

        // 1. 获取拉取天数和最大重试次数配置
        int pullDays = 3;
        int maxRetry = 3;
        try {
            String daysStr = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ORDER_PULL_DAYS);
            String retryStr = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ORDER_PULL_MAX_RETRY);
            if (daysStr != null) pullDays = Integer.parseInt(daysStr);
            if (retryStr != null) maxRetry = Integer.parseInt(retryStr);
        } catch (Exception ignore) {
        }

        // 2. 查询待拉取任务（status=0)
        final int finalMaxRetry = maxRetry;
        List<OrderPullTask> taskList = orderPullTaskService.lambdaQuery()
                .and(q -> q.eq(OrderPullTask::getStatus, 0))
                .orderByAsc(OrderPullTask::getStartTime, OrderPullTask::getPageNo)
                .last("limit 10")
                .list();

        // 3. 如果没有可执行任务，创建一条初始任务
        if (taskList.isEmpty()) {
            createInitialTask(pullDays);
            return;
        }

        // 4. 处理现有任务
        for (OrderPullTask task : taskList) {
            // 尝试抢占任务（乐观锁，status=0/3->1）
            boolean locked = orderPullTaskService.lambdaUpdate()
                    .set(OrderPullTask::getStatus, 1)
                    .set(OrderPullTask::getLastPullTime, new Date())
                    .eq(OrderPullTask::getId, task.getId())
                    .in(OrderPullTask::getStatus, 0, 3)
                    .update();
            if (!locked) continue;

            try {
                // 拉取订单并处理
                String nextPageToken = tiktokOrderSyncService.syncTiktokOrders(
                        task.getNextPageToken(), task.getStartTime(), task.getEndTime());

                // 处理分页：如果有下一页，生成新任务
                if (nextPageToken != null && !nextPageToken.isEmpty()) {
                    OrderPullTask nextTask = new OrderPullTask();
                    nextTask.setPlatform(task.getPlatform());
                    nextTask.setStartTime(task.getStartTime());
                    nextTask.setEndTime(task.getEndTime());
                    nextTask.setBatchNo(task.getBatchNo());
                    nextTask.setPageNo(task.getPageNo() + 1);
                    nextTask.setNextPageToken(nextPageToken);
                    nextTask.setStatus(0);
                    nextTask.setRetryCount(0);
                    nextTask.setCreateTime(new Date());
                    nextTask.setUpdateTime(new Date());
                    orderPullTaskService.save(nextTask);
                }

                // 标记当前任务成功
                orderPullTaskService.lambdaUpdate()
                        .set(OrderPullTask::getStatus, 2)
                        .set(OrderPullTask::getUpdateTime, new Date())
                        .eq(OrderPullTask::getId, task.getId())
                        .update();
            } catch (Exception e) {
                logger.error("TikTok订单拉取任务失败，taskId={}, pageNo={}, err={}", task.getId(), task.getPageNo(), e.getMessage(), e);

                // 标记失败并递增重试次数
                int newRetryCount = task.getRetryCount() + 1;
                orderPullTaskService.lambdaUpdate()
                        .set(OrderPullTask::getStatus, 3)
                        .set(OrderPullTask::getRetryCount, newRetryCount)
                        .set(OrderPullTask::getUpdateTime, new Date())
                        .eq(OrderPullTask::getId, task.getId())
                        .update();

                // 达到最大重试次数，推送到失败队列
                if (newRetryCount >= maxRetry) {
                    redisUtil.lPush(Constants.TIKTOK_ORDER_PULL_FAILED_QUEUE, task.getId());
                    logger.warn("TikTok订单拉取任务已达最大重试次数，推送到失败队列，taskId={}", task.getId());
                }
            }
        }
    }

    /**
     * 创建初始任务，查询时间范围内的第一页
     */
    private void createInitialTask(int pullDays) {
        try {
            // 计算时间范围：当前时间往前推pullDays天
            Date endTime = new Date();
            Date startTime = new Date(endTime.getTime() - pullDays * 24L * 60 * 60 * 1000);

            // 生成批次号
            String batchNo = "BATCH_" + System.currentTimeMillis();

            // 创建初始任务
            OrderPullTask initialTask = new OrderPullTask();
            initialTask.setPlatform("tiktok");
            initialTask.setStartTime(startTime);
            initialTask.setEndTime(endTime);
            initialTask.setBatchNo(batchNo);
            initialTask.setPageNo(1);
            initialTask.setNextPageToken(null); // 第一页不需要token
            initialTask.setStatus(0);
            initialTask.setRetryCount(0);
            initialTask.setCreateTime(new Date());
            initialTask.setUpdateTime(new Date());

            orderPullTaskService.save(initialTask);
            logger.info("创建TikTok订单拉取初始任务，时间范围：{} 至 {}", startTime, endTime);
        } catch (Exception e) {
            logger.error("创建TikTok订单拉取初始任务失败", e);
        }
    }

    /**
     * 定时消费失败队列，尝试补偿拉取失败的任务
     */
    @Scheduled(fixedDelay = 60000)
    public void consumeFailedQueue() {
        String queueKey = Constants.TIKTOK_ORDER_PULL_FAILED_QUEUE;
        Long size = redisUtil.getListSize(queueKey);
        if (size == null || size == 0) return;
        for (int i = 0; i < size; i++) {
            Object taskIdObj = redisUtil.getRightPop(queueKey, 5L);
            if (taskIdObj == null) continue;
            Long taskId;
            try {
                taskId = Long.valueOf(taskIdObj.toString());
            } catch (Exception e) {
                logger.error("[补偿] 解析失败队列taskId异常: {}", taskIdObj);
                continue;
            }
            OrderPullTask task = orderPullTaskService.getById(taskId);
            if (task == null) {
                logger.warn("[补偿] 失败队列任务不存在, taskId={}", taskId);
                continue;
            }
            try {
                // 再次尝试拉取
                String nextPageToken = tiktokOrderSyncService.syncTiktokOrders(
                        task.getNextPageToken(), task.getStartTime(), task.getEndTime());
                // 分页处理（如有需要）
                if (nextPageToken != null && !nextPageToken.isEmpty()) {
                    OrderPullTask nextTask = new OrderPullTask();
                    nextTask.setPlatform(task.getPlatform()); // 设置平台标识
                    nextTask.setStartTime(task.getStartTime());
                    nextTask.setEndTime(task.getEndTime());
                    nextTask.setBatchNo(task.getBatchNo()); // 设置批次号
                    nextTask.setPageNo(task.getPageNo() + 1);
                    nextTask.setNextPageToken(nextPageToken);
                    nextTask.setStatus(0);
                    nextTask.setRetryCount(0);
                    nextTask.setCreateTime(new Date());
                    nextTask.setUpdateTime(new Date());
                    orderPullTaskService.save(nextTask);
                }
                // 更新当前任务为成功
                orderPullTaskService.lambdaUpdate()
                        .set(OrderPullTask::getStatus, 2)
                        .set(OrderPullTask::getUpdateTime, new Date())
                        .eq(OrderPullTask::getId, task.getId())
                        .update();
                logger.info("[补偿] 失败任务补偿成功, taskId={}", taskId);
            } catch (Exception e) {
                // 补偿失败，重新放回队列
                redisUtil.lPush(queueKey, taskId);
                logger.error("[补偿] 失败任务补偿再次失败, taskId={}, err={}", taskId, e.getMessage(), e);
            }
        }
    }
} 