package com.genco.admin.controller;

import com.genco.common.model.finance.UserRecharge;
import com.genco.common.request.PayCallbackRequest;
import com.genco.service.service.CallbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


/**
 * 支付回调
 */
@Slf4j
@RestController
@RequestMapping("api/admin/payment/callback")
@Api(tags = "支付回调")
public class CallbackController {

    @Autowired
    private CallbackService callbackService;

    /**
     * 微信支付回调
     */
    @ApiOperation(value = "微信支付回调")
    @RequestMapping(value = "/wechat", method = RequestMethod.POST)
    public String weChat(@RequestBody String request) {
        log.info("微信支付回调 request ===> {}", request);
        String response = callbackService.weChat(request);
        log.info("微信支付回调 response ===> {}", response);
        return response;
    }

    /**
     * HaiPay支付回调
     */
    @ApiOperation(value = "HaiPay支付回调")
    @RequestMapping(value = "/haipay", method = RequestMethod.POST)
    public String haiPay(@RequestBody String request) {
        log.info("HaiPay支付回调 request ===> {}", request);
        String response = callbackService.haiPay(request);
        log.info("HaiPay支付回调 response ===> {}", response);
        return response;
    }

    /**
     * 微信退款回调
     */
    @ApiOperation(value = "微信退款回调")
    @RequestMapping(value = "/wechat/refund", method = RequestMethod.POST)
    public String weChatRefund(@RequestBody String request) {
        log.info("微信退款回调 request ===> {}", request);
        String response = callbackService.weChatRefund(request);
        log.info("微信退款回调 response ===> {}", response);
        return response;
    }

    /**
     * 通用支付回调
     */
    @ApiOperation(value = "通用支付回调")
    @RequestMapping(value = "/pay", method = RequestMethod.POST)
    public String payCallback(@RequestBody PayCallbackRequest request) {
        log.info("支付回调 request ===> {}", request);
        // 组装UserRecharge对象
        UserRecharge userRecharge = new UserRecharge();
        userRecharge.setOrderId(request.getOrderNo());
        userRecharge.setOutTradeNo(request.getOutTradeNo());
        userRecharge.setPayChannel(request.getPayChannel());
        userRecharge.setPayTime(request.getPayTime());
        // 这里可以根据需要补充更多字段
        try {
            boolean result = callbackService.payRechargeCallback(userRecharge);
            log.info("支付回调处理结果: {}", result ? "success" : "fail");
            return result ? "success" : "fail";
        } catch (Exception e) {
            log.error("支付回调处理异常", e);
            return "fail";
        }
    }

}



