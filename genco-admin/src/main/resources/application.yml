# CRMEB 相关配置
crmeb:
  version: CRMEB-JAVA-KY-v1.3.4 # 当前代码版本
  domain: #配合swagger使用 # 待部署域名
  wechat-api-url:  #请求微信接口中专服务器
  wechat-js-api-debug: false #微信js api系列是否开启调试模式
  wechat-js-api-beta: true #微信js api是否是beta版本
  asyncConfig: false #是否同步config表数据到redis
  asyncWeChatProgramTempList: false #是否同步小程序公共模板库
  imagePath: ./crmebimage/ # 服务器图片路径配置 斜杠结尾
  database: mysql

# 配置端口
server:
  port: 8082
  servlet:
    context-path: /         # 访问path
  tomcat:
    uri-encoding: UTF-8     # 默认编码格式
    max-threads: 1000       # 最大线程数量 默认200
    min-spare-threads: 30   # 初始化启动线程数量

spring:
  profiles:
    #  配置的环境
    active: dev
  servlet:
    multipart:
      max-file-size: 50MB #设置单个文件大小
      max-request-size: 50MB #设置单次请求文件的总大小
  application:
    name: genco-admin #这个很重要，这在以后的服务与服务之间相互调用一般都是根据这个name
  jackson:
    locale: zh_CN
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  #  数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: jdbc:mysql://*************:53306/genco_data?useUnicode=true&allowPublicKeyRetrieval=true&characterEncoding=utf-8&useSSL=false&serverTimeZone=GMT+8
    username: root
    password: mysql-develop
  redis:
    host: 127.0.0.1 #地址
    port: 6379 #端口
    password:
    timeout: 10000 # 连接超时时间（毫秒）
    database: 15 #默认数据库
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1 # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10 # 连接池中的最大空闲连接
        min-idle: 0 # 连接池中的最小空闲连接
        time-between-eviction-runs: -1 #逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1


debug: true
logging:
  level:
    io.swagger.*: error
    com.zbjk.crmeb: debug
    org.springframework.boot.autoconfigure: ERROR
  config: classpath:logback-spring.xml
  file:
    path: ./genco_log

# mybatis 配置
mybatis-plus:
  mapper-locations: classpath*:mapper/*/*Mapper.xml #xml扫描，多个目录用逗号或者分号分隔（告诉 Mapper 所对应的 XML 文件位置）
  typeAliasesPackage: com.genco.**.model
  # 配置slq打印日志
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      #      logic-delete-field: isDel  #全局逻辑删除字段值 3.3.0开始支持，详情看下面。
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)


#swagger 配置
swagger:
  basic:
    enable: true #是否开启
    check: false #是否打开验证
    username: #访问swagger的账号
    password: #访问swagger的密码
