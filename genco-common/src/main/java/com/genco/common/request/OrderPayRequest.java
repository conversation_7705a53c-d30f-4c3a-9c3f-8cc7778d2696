package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * 支付订单参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "OrderPayRequest对象", description = "订单支付")
public class OrderPayRequest {

    @ApiModelProperty(value = "业务类型")
    @NotNull(message = "业务类型：1-recharge_order，2-shopping_order")
    private String bizType;

    @ApiModelProperty(value = "订单编号")
    @NotNull(message = "订单编号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "支付类型：haipay-HaiPay支付，yue-余额支付，xendit-Xendit支付")
    @NotNull(message = "支付类型不能为空")
    private String payType;

    @ApiModelProperty(value = "支付渠道:haipay-HaiPay，xendit-Xendit")
    @NotNull(message = "支付渠道不能为空")
    private String payChannel;

    @ApiModelProperty(value = "支付来源：app，h5")
    private String from;
}
