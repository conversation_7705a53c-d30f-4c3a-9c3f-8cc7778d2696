package com.genco.common.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 用户提现表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "UserExtractRequest对象", description = "用户提现")
public class UserExtractRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "姓名")
    @NotBlank(message = "提现用户名称必须填写")
    @Length(max = 64, message = "提现用户名称不能超过64个字符")
    @JsonProperty(value = "name")
    private String realName;

    @ApiModelProperty(value = "提现方式| alipay=支付宝,bank=银行卡,wallet=电子钱包", allowableValues = "range[wallet,bank]")
    @NotBlank(message = "请选择提现方式，银行卡|电子钱包")
    private String extractType;

    @ApiModelProperty(value = "银行卡")
    @JsonProperty(value = "cardum")
    private String bankCode;

    @ApiModelProperty(value = "提现银行名称")
    private String bankName;

    @ApiModelProperty(value = "电子钱包类型")
    private String walletCode;

    @ApiModelProperty(value = "电子钱包账号")
    private String walletAccount;

    @ApiModelProperty(value = "提现金额")
    @JsonProperty(value = "amount")
    @NotNull(message = "请输入提现金额")
    private BigDecimal extractPrice;

    @ApiModelProperty(value = "备注")
    private String mark;

    @ApiModelProperty(value = "提现手续费")
    private BigDecimal serviceFee;

    @ApiModelProperty(value = "实际到账金额")
    private BigDecimal actualAmount;
}
