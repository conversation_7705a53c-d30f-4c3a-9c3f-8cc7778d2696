package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "BatchOperationRequest对象", description = "批量操作请求对象")
public class BatchOperationRequest {
    @ApiModelProperty(value = "商品ID列表", required = true)
    private List<Integer> ids;

    @ApiModelProperty(value = "操作类型（仅删除时用，可选：recycle/delete）")
    private String type;
} 