package com.genco.common.utils;

import java.util.*;

/**
 * 签名工具类
 */
public class SignUtils {

    /**
     * 获取签名字符串
     *
     * @param obj       对象
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String getSign(Object obj, String secretKey) {
        if (obj == null) {
            throw new IllegalArgumentException("签名对象不能为空");
        }
        if (secretKey == null || secretKey.trim().isEmpty()) {
            throw new IllegalArgumentException("密钥不能为空");
        }
        
        Map<String, Object> map;
        if (obj instanceof Map) {
            map = (Map<String, Object>) obj;
        } else {
            map = BeanMapTool.beanToMap(obj);
        }
        
        if (map == null || map.isEmpty()) {
            throw new IllegalArgumentException("签名对象转换后为空");
        }
        
        Set<String> keys = map.keySet();
        List<String> list = new ArrayList<>(keys);
        Collections.sort(list);
        // 构造签名键值对的格式
        StringBuilder sb = new StringBuilder();
        // 构造签名键值对的格式
        for (String key : list) {
            Object val = map.get(key);
            // 如果key不为sign_type或sign，以及key不为空，则拼接进签名字符串。
            if (!("".equals(val) || val == null || Arrays.asList("sign_type", "sign").contains(key))) {
                sb.append(key).append("=").append(val).append("&");
            }
        }
        sb.append("key=").append(secretKey);
        return sb.toString();
    }
}