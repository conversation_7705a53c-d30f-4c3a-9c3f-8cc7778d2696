package com.genco.common.utils;

import org.springframework.cglib.beans.BeanMap;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Bean和Map互转工具类
 */
public class BeanMapTool {
    
    /**
     * Bean转Map
     *
     * @param bean Bean对象
     * @param <T>  泛型
     * @return Map
     */
    public static <T> Map<String, Object> beanToMap(T bean) {
        if (bean == null) {
            throw new IllegalArgumentException("Bean对象不能为空");
        }
        
        try {
            // 首先尝试使用CGLIB的BeanMap
            BeanMap beanMap = BeanMap.create(bean);
            Map<String, Object> map = new HashMap<>();
            beanMap.forEach((key, value) -> map.put(String.valueOf(key), value));
            return map;
        } catch (Exception e) {
            // 如果CGLIB失败，使用反射方式
            return beanToMapByReflection(bean);
        }
    }
    
    /**
     * 使用反射方式将Bean转换为Map
     *
     * @param bean Bean对象
     * @param <T>  泛型
     * @return Map
     */
    private static <T> Map<String, Object> beanToMapByReflection(T bean) {
        try {
            Map<String, Object> map = new HashMap<>();
            Class<?> clazz = bean.getClass();
            
            // 获取所有字段（包括私有字段）
            Field[] fields = clazz.getDeclaredFields();
            
            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();
                Object fieldValue = field.get(bean);
                map.put(fieldName, fieldValue);
            }
            
            return map;
        } catch (Exception e) {
            throw new IllegalArgumentException("Bean转Map失败（反射方式），Bean类型: " + bean.getClass().getName() + ", 错误: " + e.getMessage(), e);
        }
    }
} 