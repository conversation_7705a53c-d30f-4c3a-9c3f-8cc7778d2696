package com.genco.common.utils;

import org.apache.commons.codec.digest.DigestUtils;

/**
 * 通用工具类
 */
public class CommonUtil {

    /**
     * 随机生成密码
     *
     * @param phone 手机号
     * @return 密码
     * 使用des方式加密
     */
    public static String createPwd(String phone) {
        String password = "Abc" + CrmebUtil.randomCount(10000, 99999);
        return CrmebUtil.encryptPassword(password, phone);
    }

    /**
     * 随机生成用户昵称
     *
     * @param phone 手机号
     * @return 昵称
     */
    public static String createNickName(String phone) {
        return DigestUtils.md5Hex(phone + DateUtil.getNowTime()).
                subSequence(0, 12).
                toString();
    }

    /**
     * 生成5个大写字母+1个数字的邀请码，随机组合成6位字符串
     * @return 邀请码
     */
    public static String generateInviteCode() {
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String digits = "0123456789";
        char[] code = new char[6];
        // 先生成5个字母
        for (int i = 0; i < 5; i++) {
            code[i] = letters.charAt((int) (Math.random() * letters.length()));
        }
        // 最后1位为数字
        code[5] = digits.charAt((int) (Math.random() * digits.length()));
        // 打乱顺序
        for (int i = code.length - 1; i > 0; i--) {
            int j = (int) (Math.random() * (i + 1));
            char temp = code[i];
            code[i] = code[j];
            code[j] = temp;
        }
        return new String(code);
    }
}
