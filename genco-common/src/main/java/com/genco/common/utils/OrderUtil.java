package com.genco.common.utils;

import com.github.pagehelper.util.StringUtil;

/**
 * 订单处理工具类
 */
public class OrderUtil {

    /**
     * @param status The current status of the order. Possible options are:
     *               - UNSPECIFIED: The status of the order is undefined. It might be updated later.
     *               - ORDERED: The order has been placed, but the commission has not been settled. But an estimated commission is available.
     *               - SETTLED: The commission of the order is already settled.
     *               - REFUNDED: The order has been returned/refunded/canceled by the buyer, and no commission will be settled.
     *               - FROZEN: Possible fraud has been detected regarding the order. The commission will be unfrozen after the fraud is resolved.
     *               - DEDUCTED: Additional deduction from your balance account.
     * @return 返回状态值
     * <p>
     * 0：待发货；1：待收货；2：已收货，待评价；3：已完成
     * ——
     * 4：UNSPECIFIED
     * 5：ORDERED
     * 6：SETTLED
     * 7：REFUNDED
     * 8：FROZEN
     * 9：DEDUCTED
     */
    public static Integer orderStatusMapping(String status) {
        if (StringUtil.isEmpty(status)) {
            return 4;
        }
        switch (status) {
            case "ORDERED":
                return 5;
            case "SETTLED":
                return 6;
            case "REFUNDED":
                return 7;
            case "FROZEN":
                return 8;
            case "DEDUCTED":
                return 9;
            default:
                return 4;
        }
    }

    /**
     * 从字符串中解析出uid数值
     *
     * @param tag
     * @return
     */
    public static Integer extraUidFromTagStr(String tag) {
        if (tag == null) {
            return 0;
        }
        try {
            return Integer.parseInt(tag);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

}
