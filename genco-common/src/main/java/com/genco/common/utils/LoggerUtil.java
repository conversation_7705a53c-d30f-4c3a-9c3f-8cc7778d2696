package com.genco.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 日志工具类
 * 支持摘要日志(digest)和详细日志(detail)
 * 基于SLF4J + Logback实现
 *
 * <AUTHOR>
 */
public class LoggerUtil {

    private static final Map<String, Logger> LOGGER_CACHE = new ConcurrentHashMap<>();

    /**
     * 获取Logger实例
     *
     * @param clazz 类
     * @return Logger实例
     */
    public static Logger getLogger(Class<?> clazz) {
        return getLogger(clazz.getName());
    }

    /**
     * 获取Logger实例
     *
     * @param name 日志名称
     * @return Logger实例
     */
    public static Logger getLogger(String name) {
        return LOGGER_CACHE.computeIfAbsent(name, LoggerFactory::getLogger);
    }

    /**
     * 打印摘要日志 - DEBUG级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void digestDebug(Logger logger, String message) {
        if (logger.isDebugEnabled()) {
            logger.debug("[DIGEST] " + message);
        }
    }

    /**
     * 打印摘要日志 - INFO级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void digestInfo(Logger logger, String message) {
        logger.info("[DIGEST] " + message);
    }

    /**
     * 打印摘要日志 - WARN级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void digestWarn(Logger logger, String message) {
        logger.warn("[DIGEST] " + message);
    }

    /**
     * 打印摘要日志 - ERROR级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void digestError(Logger logger, String message) {
        logger.error("[DIGEST] " + message);
    }

    /**
     * 打印摘要日志 - ERROR级别（带异常）
     *
     * @param logger    Logger实例
     * @param message   日志消息
     * @param throwable 异常
     */
    public static void digestError(Logger logger, String message, Throwable throwable) {
        logErrorWithStackTrace(logger, "[DIGEST]", message, throwable);
    }

    /**
     * 打印详细日志 - DEBUG级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void detailDebug(Logger logger, String message) {
        if (logger.isDebugEnabled()) {
            logger.debug("[DETAIL] " + message);
        }
    }

    /**
     * 打印详细日志 - INFO级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void detailInfo(Logger logger, String message) {
        logger.info("[DETAIL] " + message);
    }

    /**
     * 打印详细日志 - WARN级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void detailWarn(Logger logger, String message) {
        logger.warn("[DETAIL] " + message);
    }

    /**
     * 打印详细日志 - ERROR级别
     *
     * @param logger  Logger实例
     * @param message 日志消息
     */
    public static void detailError(Logger logger, String message) {
        logger.error("[DETAIL] " + message);
    }

    /**
     * 打印详细日志 - ERROR级别（带异常）
     *
     * @param logger    Logger实例
     * @param message   日志消息
     * @param throwable 异常
     */
    public static void detailError(Logger logger, String message, Throwable throwable) {
        logErrorWithStackTrace(logger, "[DETAIL]", message, throwable);
    }

    /**
     * 打印摘要日志 - 使用类名获取Logger
     *
     * @param clazz   类
     * @param message 日志消息
     */
    public static void digestInfo(Class<?> clazz, String message) {
        digestInfo(getLogger(clazz), message);
    }

    /**
     * 打印摘要日志 - 使用类名获取Logger
     *
     * @param clazz     类
     * @param message   日志消息
     * @param throwable 异常
     */
    public static void digestError(Class<?> clazz, String message, Throwable throwable) {
        digestError(getLogger(clazz), message, throwable);
    }

    /**
     * 打印详细日志 - 使用类名获取Logger
     *
     * @param clazz   类
     * @param message 日志消息
     */
    public static void detailInfo(Class<?> clazz, String message) {
        detailInfo(getLogger(clazz), message);
    }

    /**
     * 打印详细日志 - 使用类名获取Logger
     *
     * @param clazz     类
     * @param message   日志消息
     * @param throwable 异常
     */
    public static void detailError(Class<?> clazz, String message, Throwable throwable) {
        detailError(getLogger(clazz), message, throwable);
    }

    /**
     * 打印摘要日志 - 使用字符串名称获取Logger
     *
     * @param name    日志名称
     * @param message 日志消息
     */
    public static void digestInfo(String name, String message) {
        digestInfo(getLogger(name), message);
    }

    /**
     * 打印摘要日志 - 使用字符串名称获取Logger
     *
     * @param name      日志名称
     * @param message   日志消息
     * @param throwable 异常
     */
    public static void digestError(String name, String message, Throwable throwable) {
        digestError(getLogger(name), message, throwable);
    }

    /**
     * 打印详细日志 - 使用字符串名称获取Logger
     *
     * @param name    日志名称
     * @param message 日志消息
     */
    public static void detailInfo(String name, String message) {
        detailInfo(getLogger(name), message);
    }

    /**
     * 打印详细日志 - 使用字符串名称获取Logger
     *
     * @param name      日志名称
     * @param message   日志消息
     * @param throwable 异常
     */
    public static void detailError(String name, String message, Throwable throwable) {
        detailError(getLogger(name), message, throwable);
    }

    /**
     * 格式化日志消息
     *
     * @param format 格式字符串
     * @param args   参数
     * @return 格式化后的消息
     */
    public static String format(String format, Object... args) {
        try {
            return String.format(format, args);
        } catch (Exception e) {
            return format + " [格式化失败: " + e.getMessage() + "]";
        }
    }
    
    /**
     * 获取异常的完整堆栈信息
     *
     * @param throwable 异常
     * @return 完整的堆栈信息字符串
     */
    public static String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "null";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("异常类型: ").append(throwable.getClass().getName()).append("\n");
        sb.append("异常消息: ").append(throwable.getMessage()).append("\n");
        sb.append("完整堆栈信息:\n");
        
        // 获取当前异常的堆栈
        StackTraceElement[] stackTrace = throwable.getStackTrace();
        for (int i = 0; i < stackTrace.length; i++) {
            sb.append("\tat ").append(stackTrace[i].toString()).append("\n");
        }
        
        // 获取原因异常
        Throwable cause = throwable.getCause();
        if (cause != null) {
            sb.append("原因异常: ").append(cause.getClass().getName()).append(": ").append(cause.getMessage()).append("\n");
            StackTraceElement[] causeStackTrace = cause.getStackTrace();
            for (int i = 0; i < causeStackTrace.length; i++) {
                sb.append("\tat ").append(causeStackTrace[i].toString()).append("\n");
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 增强的错误日志记录方法
     *
     * @param logger    Logger实例
     * @param prefix    日志前缀
     * @param message   日志消息
     * @param throwable 异常
     */
    private static void logErrorWithStackTrace(Logger logger, String prefix, String message, Throwable throwable) {
        if (throwable != null) {
            // 记录原始消息和异常
            logger.error(prefix + " " + message, throwable);
            
            // 额外记录详细的堆栈信息（如果日志级别允许）
            if (logger.isErrorEnabled()) {
                String stackTrace = getStackTrace(throwable);
                logger.error(prefix + " 详细堆栈信息:\n" + stackTrace);
            }
        } else {
            logger.error(prefix + " " + message);
        }
    }

    /**
     * 打印摘要日志 - 格式化消息
     *
     * @param logger Logger实例
     * @param format 格式字符串
     * @param args   参数
     */
    public static void digestInfo(Logger logger, String format, Object... args) {
        digestInfo(logger, format(format, args));
    }

    /**
     * 打印详细日志 - 格式化消息
     *
     * @param logger Logger实例
     * @param format 格式字符串
     * @param args   参数
     */
    public static void detailInfo(Logger logger, String format, Object... args) {
        detailInfo(logger, format(format, args));
    }

    /**
     * 打印摘要日志 - 格式化消息（使用类名）
     *
     * @param clazz  类
     * @param format 格式字符串
     * @param args   参数
     */
    public static void digestInfo(Class<?> clazz, String format, Object... args) {
        digestInfo(getLogger(clazz), format, args);
    }

    /**
     * 打印详细日志 - 格式化消息（使用类名）
     *
     * @param clazz  类
     * @param format 格式字符串
     * @param args   参数
     */
    public static void detailInfo(Class<?> clazz, String format, Object... args) {
        detailInfo(getLogger(clazz), format, args);
    }
    
    /**
     * 打印摘要日志 - 格式化消息（带异常）
     *
     * @param logger    Logger实例
     * @param format    格式字符串
     * @param throwable 异常
     * @param args      参数
     */
    public static void digestError(Logger logger, String format, Throwable throwable, Object... args) {
        logErrorWithStackTrace(logger, "[DIGEST]", format(format, args), throwable);
    }
    
    /**
     * 打印详细日志 - 格式化消息（带异常）
     *
     * @param logger    Logger实例
     * @param format    格式字符串
     * @param throwable 异常
     * @param args      参数
     */
    public static void detailError(Logger logger, String format, Throwable throwable, Object... args) {
        logErrorWithStackTrace(logger, "[DETAIL]", format(format, args), throwable);
    }
    
    /**
     * 打印摘要日志 - 格式化消息（使用类名，带异常）
     *
     * @param clazz     类
     * @param format    格式字符串
     * @param throwable 异常
     * @param args      参数
     */
    public static void digestError(Class<?> clazz, String format, Throwable throwable, Object... args) {
        digestError(getLogger(clazz), format, throwable, args);
    }
    
    /**
     * 打印详细日志 - 格式化消息（使用类名，带异常）
     *
     * @param clazz     类
     * @param format    格式字符串
     * @param throwable 异常
     * @param args      参数
     */
    public static void detailError(Class<?> clazz, String format, Throwable throwable, Object... args) {
        detailError(getLogger(clazz), format, throwable, args);
    }
    
    /**
     * 打印摘要日志 - 格式化消息（使用字符串名称，带异常）
     *
     * @param name      日志名称
     * @param format    格式字符串
     * @param throwable 异常
     * @param args      参数
     */
    public static void digestError(String name, String format, Throwable throwable, Object... args) {
        digestError(getLogger(name), format, throwable, args);
    }
    
    /**
     * 打印详细日志 - 格式化消息（使用字符串名称，带异常）
     *
     * @param name      日志名称
     * @param format    格式字符串
     * @param throwable 异常
     * @param args      参数
     */
    public static void detailError(String name, String format, Throwable throwable, Object... args) {
        detailError(getLogger(name), format, throwable, args);
    }
}