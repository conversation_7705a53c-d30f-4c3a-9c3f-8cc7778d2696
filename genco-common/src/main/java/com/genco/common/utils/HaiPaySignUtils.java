package com.genco.common.utils;

import cn.hutool.core.util.StrUtil;
import com.genco.common.vo.HaiPayRequestVo;
import com.genco.common.vo.HaiPayQueryRequestVo;
import com.genco.common.vo.HaiPayResponseVo;
import com.genco.common.vo.HaiPayQueryResponseVo;

import java.util.*;

/**
 * HaiPay签名工具类
 */
public class HaiPaySignUtils {

    /**
     * 为HaiPayRequestVo生成签名字符串
     *
     * @param request HaiPay请求对象
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String getHaiPayRequestSign(HaiPayRequestVo request, String secretKey) {
        if (request == null) {
            throw new IllegalArgumentException("HaiPay请求对象不能为空");
        }
        if (StrUtil.isBlank(secretKey)) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("appId", request.getAppId());
        params.put("orderId", request.getOrderId());
        params.put("amount", request.getAmount());
        params.put("phone", request.getPhone());
        params.put("email", request.getEmail());
        params.put("name", request.getName());
        params.put("inBankCode", request.getInBankCode());
        params.put("payType", request.getPayType());
        params.put("callBackUrl", request.getCallBackUrl());
        params.put("partnerUserId", request.getPartnerUserId());

        return buildSignString(params, secretKey);
    }

    /**
     * 为HaiPayQueryRequestVo生成签名字符串
     *
     * @param request HaiPay查询请求对象
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String getHaiPayQueryRequestSign(HaiPayQueryRequestVo request, String secretKey) {
        if (request == null) {
            throw new IllegalArgumentException("HaiPay查询请求对象不能为空");
        }
        if (StrUtil.isBlank(secretKey)) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("appId", request.getAppId());
        params.put("orderId", request.getOrderId());

        return buildSignString(params, secretKey);
    }

    /**
     * 为HaiPay响应数据生成签名字符串
     *
     * @param data HaiPay响应数据
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String getHaiPayResponseSign(HaiPayResponseVo.HaiPayDataVo data, String secretKey) {
        if (data == null) {
            throw new IllegalArgumentException("HaiPay响应数据不能为空");
        }
        if (StrUtil.isBlank(secretKey)) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("orderId", data.getOrderId());
        params.put("orderNo", data.getOrderNo());
        params.put("payUrl", data.getPayUrl());
        params.put("bankCode", data.getBankCode());
        params.put("bankNo", data.getBankNo());
        params.put("qrCode", data.getQrCode());

        return buildSignString(params, secretKey);
    }

    /**
     * 为HaiPay查询响应数据生成签名字符串
     *
     * @param data HaiPay查询响应数据
     * @param secretKey 密钥
     * @return 签名字符串
     */
    public static String getHaiPayQueryResponseSign(HaiPayQueryResponseVo.HaiPayQueryDataVo data, String secretKey) {
        if (data == null) {
            throw new IllegalArgumentException("HaiPay查询响应数据不能为空");
        }
        if (StrUtil.isBlank(secretKey)) {
            throw new IllegalArgumentException("密钥不能为空");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("orderId", data.getOrderId());
        params.put("orderNo", data.getOrderNo());
        params.put("status", data.getStatus());
        params.put("amount", data.getAmount());
        params.put("actualAmount", data.getActualAmount());
        params.put("fee", data.getFee());
        params.put("payTime", data.getPayTime());
        params.put("bankCode", data.getBankCode());
        params.put("bankNo", data.getBankNo());
        params.put("transactionId", data.getTransactionId());

        return buildSignString(params, secretKey);
    }

    /**
     * 构建签名字符串
     *
     * @param params 参数Map
     * @param secretKey 密钥
     * @return 签名字符串
     */
    private static String buildSignString(Map<String, Object> params, String secretKey) {
        // 过滤空值和sign字段
        Map<String, Object> filteredParams = new HashMap<>();
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            Object value = entry.getValue();
            if (value != null && !"".equals(value.toString()) && !"sign".equals(entry.getKey())) {
                filteredParams.put(entry.getKey(), value);
            }
        }

        // 按键名排序
        List<String> keys = new ArrayList<>(filteredParams.keySet());
        Collections.sort(keys);

        // 构建签名字符串
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            sb.append(key).append("=").append(filteredParams.get(key)).append("&");
        }
        sb.append("key=").append(secretKey);

        return sb.toString();
    }
} 