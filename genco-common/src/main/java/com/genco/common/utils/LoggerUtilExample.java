package com.genco.common.utils;

import org.slf4j.Logger;

/**
 * LoggerUtil 使用示例
 * 基于SLF4J + Logback
 *
 * <AUTHOR>
 */
public class LoggerUtilExample {

    private static final Logger logger = LoggerUtil.getLogger(LoggerUtilExample.class);

    public static void main(String[] args) {
        // 示例1: 使用类名直接打印日志
        LoggerUtil.digestInfo(LoggerUtilExample.class, "用户登录成功，用户ID: {}", 12345);
        LoggerUtil.detailInfo(LoggerUtilExample.class, "用户登录详细信息 - IP: {}, 时间: {}, 设备: {}",
                "***********", DateUtil.getNowTime(), "iPhone");

        // 示例2: 使用Logger实例打印日志
        LoggerUtil.digestInfo(logger, "订单创建成功，订单号: {}", "ORD20231201001");
        LoggerUtil.detailInfo(logger, "订单详细信息 - 商品: {}, 数量: {}, 金额: {}",
                "iPhone 15", 1, 5999.00);

        // 示例3: 使用字符串名称获取Logger
        LoggerUtil.digestInfo("UserService", "用户注册成功");
        LoggerUtil.detailInfo("OrderService", "订单处理完成");

        // 示例4: 异常日志
        try {
            // 模拟异常
            throw new RuntimeException("模拟的业务异常");
        } catch (Exception e) {
            LoggerUtil.digestError(LoggerUtilExample.class, "业务处理失败", e);
            LoggerUtil.detailError(logger, "详细错误信息", e);
        }

        // 示例5: 不同级别的日志
        LoggerUtil.digestDebug(logger, "调试信息");
        LoggerUtil.digestWarn(logger, "警告信息");
        LoggerUtil.detailWarn(logger, "详细警告信息");
    }

    /**
     * 业务方法示例
     */
    public void processUserLogin(String userId, String ip, String device) {
        // 摘要日志 - 记录关键信息
        LoggerUtil.digestInfo(this.getClass(), "用户登录 - 用户ID: {}, IP: {}", userId, ip);

        // 详细日志 - 记录完整信息
        LoggerUtil.detailInfo(this.getClass(),
                "用户登录详细信息 - 用户ID: {}, IP: {}, 设备: {}, 时间: {}",
                userId, ip, device, DateUtil.getNowTime());

        // 业务逻辑处理...

        // 处理完成后的日志
        LoggerUtil.digestInfo(this.getClass(), "用户登录处理完成 - 用户ID: {}", userId);
    }

    /**
     * 异常处理示例
     */
    public void processOrder(String orderId) {
        try {
            LoggerUtil.digestInfo(this.getClass(), "开始处理订单 - 订单号: {}", orderId);

            // 模拟业务处理
            if (orderId == null) {
                throw new IllegalArgumentException("订单号不能为空");
            }

            // 业务逻辑...

            LoggerUtil.digestInfo(this.getClass(), "订单处理成功 - 订单号: {}", orderId);

        } catch (Exception e) {
            // 摘要错误日志
            LoggerUtil.digestError(this.getClass(), "订单处理失败 - 订单号: {}", e, orderId);

            // 详细错误日志
            LoggerUtil.detailError(this.getClass(),
                    "订单处理详细错误信息 - 订单号: {}, 错误类型: {}, 错误消息: {}",
                    e, orderId, e.getClass().getSimpleName(), e.getMessage());
        }
    }
} 