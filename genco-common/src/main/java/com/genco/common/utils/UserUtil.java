package com.genco.common.utils;

import cn.hutool.core.util.StrUtil;
import com.genco.common.constants.Constants;
import com.genco.common.token.FrontTokenComponent;
import com.genco.common.vo.LoginUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 用户工具类
 * 用于获取当前登录用户的ID，支持admin和front模块
 * 
 * <AUTHOR>
 */
@Component
public class UserUtil {

    @Autowired
    private FrontTokenComponent frontTokenComponent;

    @Autowired
    private RedisUtil redisUtil;

    // Redis 存储的key - admin模块
    private static final String TOKEN_REDIS = "TOKEN:ADMIN:";

    /**
     * 获取当前登录用户ID
     * 优先从前端token获取，如果没有则尝试从admin token获取
     * 
     * @return 用户ID，如果未登录返回null
     */
    public Integer getCurrentUserId() {
        try {
            HttpServletRequest request = getCurrentRequest();
            if (request == null) {
                return null;
            }
            
            // 尝试从前端token获取用户ID
            Integer frontUserId = getFrontUserId(request);
            if (frontUserId != null) {
                return frontUserId;
            }
            
            // 尝试从admin token获取用户ID
            Integer adminUserId = getAdminUserId(request);
            if (adminUserId != null) {
                return adminUserId;
            }
            
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取前端用户ID
     */
    private Integer getFrontUserId(HttpServletRequest request) {
        try {
            return frontTokenComponent.getUserId();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取admin用户ID
     */
    private Integer getAdminUserId(HttpServletRequest request) {
        try {
            String token = getAdminToken(request);
            if (StrUtil.isNotEmpty(token)) {
                String userKey = getAdminTokenKey(token);
                LoginUserVo loginUser = redisUtil.get(userKey);
                if (loginUser != null && loginUser.getUser() != null) {
                    return loginUser.getUser().getId();
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取admin token
     */
    private String getAdminToken(HttpServletRequest request) {
        String token = request.getHeader(Constants.HEADER_AUTHORIZATION_KEY);
        if (StrUtil.isNotEmpty(token) && token.startsWith(TOKEN_REDIS)) {
            token = token.replace(TOKEN_REDIS, "");
        }
        return token;
    }

    /**
     * 获取admin token key
     */
    private String getAdminTokenKey(String uuid) {
        return TOKEN_REDIS + uuid;
    }

    /**
     * 获取当前请求
     */
    private HttpServletRequest getCurrentRequest() {
        if (RequestContextHolder.getRequestAttributes() != null) {
            return ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
        }
        return null;
    }

    /**
     * 判断是否为admin模块请求
     */
    public boolean isAdminRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri != null && uri.startsWith("/api/admin/");
    }

    /**
     * 判断是否为front模块请求
     */
    public boolean isFrontRequest(HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri != null && uri.startsWith("/api/front/");
    }
} 