package com.genco.common.utils;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;

/**
 * 品牌编码生成工具类
 */
public class BrandCodeUtils {

    /**
     * 根据品牌名称生成编码
     * 规则：
     * 1. 如果是英文，直接转为大写并移除空格
     * 2. 如果是中文，转换为拼音后转为大写并移除空格
     * 3. 如果是中英文混合，分别处理
     * 4. 移除所有特殊字符，只保留字母和数字
     *
     * @param brandName 品牌名称
     * @return 生成的编码
     */
    public static String generateBrandCode(String brandName) {
        if (StringUtils.isBlank(brandName)) {
            throw new IllegalArgumentException("品牌名称不能为空");
        }

        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < brandName.length(); i++) {
            char c = brandName.charAt(i);
            
            if (Character.isLetterOrDigit(c)) {
                if (isChinese(c)) {
                    // 中文字符，转换为拼音
                    String pinyin = getPinyin(String.valueOf(c));
                    if (StringUtils.isNotBlank(pinyin)) {
                        result.append(pinyin.toUpperCase());
                    }
                } else {
                    // 英文字符或数字，直接添加
                    result.append(Character.toUpperCase(c));
                }
            }
            // 忽略空格和其他特殊字符
        }
        
        String code = result.toString();
        if (StringUtils.isBlank(code)) {
            throw new IllegalArgumentException("无法从品牌名称生成有效编码");
        }
        
        return code;
    }

    /**
     * 判断字符是否为中文
     */
    private static boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;
    }

    /**
     * 获取中文字符的拼音
     */
    private static String getPinyin(String chinese) {
        try {
            HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
            format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
            format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
            
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(chinese.charAt(0), format);
            if (pinyinArray != null && pinyinArray.length > 0) {
                return pinyinArray[0];
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            // 转换失败，返回空字符串
        }
        return "";
    }

    /**
     * 生成唯一的品牌编码
     * 如果编码已存在，会在后面添加数字后缀
     *
     * @param brandName 品牌名称
     * @param existingCodes 已存在的编码列表
     * @return 唯一的编码
     */
    public static String generateUniqueBrandCode(String brandName, java.util.List<String> existingCodes) {
        String baseCode = generateBrandCode(brandName);
        String uniqueCode = baseCode;
        int suffix = 1;
        
        while (existingCodes.contains(uniqueCode)) {
            uniqueCode = baseCode + suffix;
            suffix++;
        }
        
        return uniqueCode;
    }
} 