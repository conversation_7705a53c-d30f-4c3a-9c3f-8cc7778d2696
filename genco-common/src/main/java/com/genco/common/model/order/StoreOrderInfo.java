package com.genco.common.model.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单购物详情表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("eb_store_order_info")
@ApiModel(value = "StoreOrderInfo对象", description = "订单购物详情表")
public class StoreOrderInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "订单id")
    private Integer orderId;

    @ApiModelProperty(value = "商品ID")
    private Integer productId;

    @ApiModelProperty(value = "购买东西的详细信息")
    private String info;

    @ApiModelProperty(value = "唯一id")
    @TableField(value = "`unique`")
    private String unique;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "规格属性id")
    private Integer attrValueId;

    @ApiModelProperty(value = "商品图片")
    private String image;

    @ApiModelProperty(value = "sku")
    private String sku;

    @ApiModelProperty(value = "单价")
    private BigDecimal price;

    @ApiModelProperty(value = "购买数量")
    private Integer payNum;

    @ApiModelProperty(value = "重量")
    private BigDecimal weight;

    @ApiModelProperty(value = "体积")
    private BigDecimal volume;

    @ApiModelProperty(value = "获得积分")
    private Integer giveIntegral;

    @ApiModelProperty(value = "是否评价")
    private Boolean isReply;

    @ApiModelProperty(value = "是否单独分佣")
    private Boolean isSub;

    @ApiModelProperty(value = "会员价")
    private BigDecimal vipPrice;

    @ApiModelProperty(value = "商品类型:0-普通，1-秒杀，2-砍价，3-拼团，4-视频号, 5-LinkShare")
    private Integer productType;

    /**
     * @link https://partner.tiktokshop.com/docv2/page/6736985bd99b7d02fd715482?external_id=6736985bd99b7d02fd715482
     * <p>
     * An object representing the actual bonus commission,
     * calculated by multiplying the actual commission base by the commission bonus rate.
     */
    private BigDecimal actualBonusCommission;

    /**
     * An object representing the actual base commission,
     * calculated by multiplying the actual commission base by the commission rate.
     */
    private BigDecimal actualCommission;

    /**
     * An object representing the actual commission base,
     * calculated by multiplying the product sale price by the number of products sold,
     * subtracting returned and refunded orders.
     */
    private BigDecimal actualCommissionBase;

    /**
     * Actual creator commission reward fee.
     */
    private BigDecimal actualCreatorCommissionRewardFee;

    /**
     * An object representing the actual shop ads commission,
     * calculated by multiplying the commission base by the shop_ads_commission_rate.
     */
    private BigDecimal actualShopAdsCommission;

    /**
     * The campaign identifier associated with the order.
     * 活动ID
     */
    private String campaignId;

    /**
     * The commission bonus rate associated with the collaboration.
     * Expressed in units of hundredths of a percent formatted as a string.
     * The percent sign % is not included in the string.
     * For example, 3000 represents a 30% commission.
     */
    private BigDecimal commissionBonusRate;

    /**
     * The commission rate associated with the collaboration.
     * Expressed in units of hundredths of a percent formatted as a string.
     * The percent sign % is not included in the string.
     * For example, 3000 represents a 30% commission.
     */
    private BigDecimal commissionRate;

    /**
     * An object representing the estimated bonus commission,
     * calculated by multiplying the estimated commission base by the commission bonus rate.
     */
    private BigDecimal estimatedBonusCommission;

    /**
     * The estimated creator commission,
     * calculated by multiplying the product sales price by the total number of
     * products at the time of order creation.
     */
    private BigDecimal estimatedCommission;

    /**
     * An object representing the estimated base commission at the time of order creation.
     */
    private BigDecimal estimatedCommissionBase;

    /**
     * Estimated creator commission reward fee.
     */
    private BigDecimal estimatedCreatorCommissionRewardFee;

    /**
     * An object representing the estimated shop ads commission,
     * calculated by multiplying the estimated commission base by the shop_ads_commission_rate
     */
    private BigDecimal estimatedShopAdsCommission;

    /**
     * The total number of refunded SKUs associated with the order.
     */
    private Integer refundedQuantity;

    /**
     * The total number of returned SKUs associated with the order.
     */
    private Integer returnedQuantity;

    /**
     * The commission rate received by a creator for a sale associated
     * with a specific piece of content.
     * Expressed in units of hundredths of a percent formatted as a string.
     * The percent sign % is not included in the string.
     * For example, 3000 represents a 30% commission.
     */
    private Long shopAdsCommissionRate;

    /**
     * 标签
     * 分享链接生成的时候指定的
     */
    private String tag;

    /**
     * 内容标识
     * 分享链接生成的时候指定的
     */
    private String contentId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 外部商品ID
     */
    private String outProductId;
}
