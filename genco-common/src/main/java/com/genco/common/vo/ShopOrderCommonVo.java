package com.genco.common.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 自定义交易组件订单公共Vo
 */
@Data
public class ShopOrderCommonVo {

    /**
     * 微信侧订单id （订单id二选一）
     */
    @TableField(value = "order_id")
    private Integer orderId;

    /**
     * 商家自定义订单ID
     */
    @TableField(value = "out_order_id")
    private String outOrderId;

    /**
     * 用户的openid
     */
    private String openid;
}
