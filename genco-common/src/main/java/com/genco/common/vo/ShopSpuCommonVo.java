package com.genco.common.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 自定义交易组件商品公共Vo
 */
@Data
public class ShopSpuCommonVo {

    /**
     * 交易组件平台内部商品ID
     */
    @TableField(value = "product_id")
    private Integer productId;

    /**
     * 商家自定义商品ID
     */
    @TableField(value = "out_product_id")
    private String outProductId;

    /**
     * 默认0:获取线上数据, 1:获取草稿数据
     */
    @TableField(value = "need_edit_spu")
    private Integer needEditSpu = 0;
}
