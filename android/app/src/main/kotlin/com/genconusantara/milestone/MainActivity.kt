package com.genconusantara.milestone

import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.lifecycle.lifecycleScope
import com.tiktok.open.sdk.auth.AuthApi
import com.tiktok.open.sdk.auth.AuthRequest
import com.tiktok.open.sdk.auth.utils.PKCEUtils
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugins.GeneratedPluginRegistrant
import kotlinx.coroutines.Dispatchers
import android.content.SharedPreferences
import kotlinx.coroutines.launch
import android.content.Context

class MainActivity : FlutterActivity() {
    companion object {
        private const val TAG = "TikTokAuth"
        private const val STATE_CODE_VERIFIER = "code_verifier"
        private const val PREFS_NAME = "TikTokAuthPrefs"
        private const val RESULT_CHANNEL_NAME = "channel:tiktokLoginResult"
    }

    private lateinit var authApi: AuthApi
    private var tiktokResult: MethodChannel.Result? = null
    private val redirectUrl = "https://genconusantara.com/callback"
    private var codeVerifier = ""
    private lateinit var prefs: SharedPreferences
    private lateinit var resultChannel: MethodChannel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Log.d(TAG, "onCreate called")
        authApi = AuthApi(activity = this)
        prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
        savedInstanceState?.getString(STATE_CODE_VERIFIER)?.let {
            codeVerifier = it
            Log.d(TAG, "Restored codeVerifier: $codeVerifier")
        }
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putString(STATE_CODE_VERIFIER, codeVerifier)
        Log.d(TAG, "Saved codeVerifier: $codeVerifier")
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        Log.d(TAG, "configureFlutterEngine called")
        GeneratedPluginRegistrant.registerWith(flutterEngine)
        registerTiktokLoginChannel(flutterEngine)
        resultChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, RESULT_CHANNEL_NAME)

        // 延迟处理Intent，确保引擎就绪
        lifecycleScope.launch(Dispatchers.Main) {
            Log.d(TAG, "Processing initial intent")
            handleDeepLink(intent)
        }
    }

    private fun handleDeepLink(intent: Intent?) {
        Log.d(TAG, "handleDeepLink called with intent: ${intent?.dataString}")

        val response = authApi.getAuthResponseFromIntent(intent, redirectUrl)
        if (response == null) {
            Log.w(TAG, "No TikTok auth response in intent")
            return
        }
        if(codeVerifier.isEmpty()) {
            codeVerifier = prefs.getString(STATE_CODE_VERIFIER, "") ?: ""
            Log.d(TAG, "fetch codeVerifier from prefs: $codeVerifier")
        }

        Log.d(TAG, "Received TikTok response: " +
                "errorCode=${response.errorCode}, " +
                "authCode=${response.authCode}, " +
                "state=${response.state}, " +
                "grantedPermissions=${response.grantedPermissions}")

        lifecycleScope.launch(Dispatchers.Main) {
            try {
                if (response.errorCode == 0) {
                    Log.d(TAG, "TikTok auth successful")
                    val resultMap = mapOf(
                        "authCode" to response.authCode,
                        "state" to response.state,
                        "grantedPermissions" to response.grantedPermissions,
                        "codeVerifier" to codeVerifier
                    )
                    if (tiktokResult != null) {
                        tiktokResult?.success(resultMap)
                    }else {
                        sendLoginDataToFlutter(resultMap)
                    }

                } else {
                    Log.e(TAG, "TikTok auth failed: ${response.errorCode} - ${response.errorMsg}")
                    tiktokResult?.error(
                        response.errorCode.toString(),
                        response.errorMsg,
                        null
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "TikTok callback error", e)
                tiktokResult?.error("callback_failed", e.message, null)
            } finally {
                // 重置结果防止重复调用
                tiktokResult = null
                Log.d(TAG, "Reset tiktokResult")
            }
        }
    }

    private fun sendLoginDataToFlutter(result: Map<String, String?>) {
        resultChannel.invokeMethod("sendingLoginResult", result, object : MethodChannel.Result {
            override fun success(result: Any?) {
                Log.i("sendingLoginResult", result.toString())
            }

            override fun error(
                errorCode: String,
                errorMessage: String?,
                errorDetails: Any?
            ) {
                Log.e("sendDeviceToken", errorMessage ?: "")
            }

            override fun notImplemented() {
                Log.e("sendDeviceToken", "Not yet implemented")
            }
        });
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Log.d(TAG, "onNewIntent called with: ${intent.dataString}")
        handleDeepLink(intent)
    }

    private fun registerTiktokLoginChannel(engine: FlutterEngine) {
        val channel = MethodChannel(engine.dartExecutor.binaryMessenger, "channel:tiktokLogin")
        channel.setMethodCallHandler { call, result ->
            when (call.method) {
                "login" -> {
                    Log.d(TAG, "Login method called from Flutter")
                    this.tiktokResult = result
                    codeVerifier = PKCEUtils.generateCodeVerifier()
                    Log.d(TAG, "Generated codeVerifier: $codeVerifier")
                    prefs.edit()
                        .putString(STATE_CODE_VERIFIER, codeVerifier)
                        .apply()
                    Log.d(TAG, "prefs save codeVerifier: $codeVerifier")

                    val request = AuthRequest(
                        clientKey = "awnb2jrqphg8q3x9",
                        scope = "user.info.basic",
                        redirectUri = redirectUrl,
                        codeVerifier = codeVerifier
                    )

                    Log.d(TAG, "Starting TikTok auth with request: $request")

                    // 在主线程外执行授权请求
                    lifecycleScope.launch(Dispatchers.IO) {
                        try {
                            Log.d(TAG, "Launching TikTok auth")
                            authApi.authorize(request, AuthApi.AuthMethod.TikTokApp)
                            Log.d(TAG, "TikTok auth launched successfully")
                        } catch (e: Exception) {
                            Log.e(TAG, "TikTok auth failed", e)
                            launch(Dispatchers.Main) {
                                result.error("auth_failed", e.message, null)
                            }
                        }
                    }
                }
                else -> {
                    Log.w(TAG, "Unknown method called: ${call.method}")
                    result.notImplemented()
                }
            }
        }
    }
}